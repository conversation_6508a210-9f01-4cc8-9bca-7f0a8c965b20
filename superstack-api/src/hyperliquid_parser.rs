use anyhow::{anyhow, Result};
use sha2::{Digest, Sha256};
use std::collections::HashMap;

use crate::models::{
    hyperliquid_activity::{
        HyperliquidFill, HyperliquidNonFundingLedgerUpdate, OrderParameters, OrderStatus,
        OrderUpdate, PerpsOrder, RecordActivityRequest, TwapOrderParameters,
        TwapOrderStatusResponse, TwapSliceFill, WsUserTwapSliceFills,
    },
    DbWalletActivity,
};



/// Hyperliquid Activity Parser for batch processing
pub struct HyperliquidParser;

impl HyperliquidParser {
    /// Generate a stable hash for perps order to prevent duplicates
    /// Uses order identifiers from responses to create consistent hashes
    /// Priority: Use real transaction hash if available, otherwise use synthetic hash
    fn generate_stable_perps_order_hash(perps_order: &PerpsOrder) -> String {
        // First priority: Try to extract real transaction hash from fills
        // This ensures consistency with userFills data
        if let Some(order_response) = &perps_order.order_response {
            for response in order_response {
                match response {
                    OrderStatus::Filled { filled } => {
                        // Use unified oid format to prevent duplicate records
                        return format!("oid_{}", filled.oid);
                    }
                    OrderStatus::Resting { resting } => {
                        // Use unified oid format to prevent duplicate records
                        return format!("oid_{}", resting.oid);
                    }
                    OrderStatus::Error { error: _ } => {
                        // Continue to next response or fallback
                        continue;
                    }
                }
            }
        }

        // Second priority: Try to extract TWAP ID from TWAP response
        if let Some(twap_response) = &perps_order.twap_order_response {
            match twap_response {
                TwapOrderStatusResponse::Running { running } => {
                    return format!("twap_order_{}", running.twap_id);
                }
                TwapOrderStatusResponse::Error { error: _ } => {
                    // Continue to fallback
                }
            }
        }

        // Fallback: use timestamp-based hash (less stable but unique)
        format!("perps_order_{}", chrono::Utc::now().timestamp_millis())
    }

    /// Parse a batch of Hyperliquid activities into DbWalletActivity format
    pub fn parse_activities(request: &RecordActivityRequest) -> Result<Vec<DbWalletActivity>> {
        let mut activities = Vec::new();
        let mut errors = Vec::new();

        // Process fills if present - ENABLED: Process userFills with order-level aggregation
        if let Some(user_fills) = &request.activities.user_fills {
            tracing::info!(
                "Processing {} user fills for wallet {}",
                user_fills.len(),
                request.wallet_address
            );

            // Group fills by oid for order-level aggregation
            let mut aggregated_activities =
                match Self::aggregate_fills_by_order(user_fills, &request.wallet_address) {
                    Ok(activities) => {
                        tracing::info!(
                            "Successfully aggregated {} activities from {} fills",
                            activities.len(),
                            user_fills.len()
                        );
                        activities
                    }
                    Err(e) => {
                        tracing::error!("Failed to aggregate fills: {}", e);
                        return Err(e);
                    }
                };

            // Mark as userFill aggregated data for universal update framework
            for activity in &mut aggregated_activities {
                if let Some(ref mut metadata) = activity.metadata {
                    if let Some(metadata_obj) = metadata.as_object_mut() {
                        metadata_obj.insert(
                            "source".to_string(),
                            serde_json::Value::String("userfill_aggregated".to_string()),
                        );
                        metadata_obj.insert(
                            "data_type".to_string(),
                            serde_json::Value::String("user_fill".to_string()),
                        );
                    }
                }
            }

            activities.extend(aggregated_activities);
        }

        // Process ledger updates if present
        if let Some(user_updates) = &request.activities.user_non_funding_ledger_updates {
            for update in user_updates {
                match update.to_db_activity(&request.wallet_address) {
                    Ok(mut activity) => {
                        // Mark as nonfunding ledger for universal update framework
                        if let Some(ref mut metadata) = activity.metadata {
                            if let Some(metadata_obj) = metadata.as_object_mut() {
                                metadata_obj.insert(
                                    "source".to_string(),
                                    serde_json::Value::String("nonfunding_ledger".to_string()),
                                );
                                metadata_obj.insert(
                                    "data_type".to_string(),
                                    serde_json::Value::String("ledger_update".to_string()),
                                );
                            }
                        }
                        activities.push(activity);
                    }
                    Err(e) => {
                        errors
                            .push(format!("Failed to parse ledger update {}: {}", update.hash, e));
                        tracing::warn!("Failed to parse ledger update {}: {}", update.hash, e);
                    }
                }
            }
        }

        // Process perps order if present (single object)
        if let Some(perps_order) = &request.activities.perps_order {
            // Generate a stable hash based on order identifiers to prevent duplicates
            let synthetic_hash = Self::generate_stable_perps_order_hash(perps_order);
            let timestamp = chrono::Utc::now().timestamp_millis();

            match perps_order.to_db_activity(
                &request.wallet_address,
                &synthetic_hash,
                timestamp,
                request.is_hyperliquid_mainnet,
            ) {
                Ok(mut activity) => {
                    // Mark as perps order for universal update framework
                    if let Some(ref mut metadata) = activity.metadata {
                        if let Some(metadata_obj) = metadata.as_object_mut() {
                            metadata_obj.insert(
                                "source".to_string(),
                                serde_json::Value::String("perps_order".to_string()),
                            );
                            metadata_obj.insert(
                                "data_type".to_string(),
                                serde_json::Value::String("place_order".to_string()),
                            );
                        }
                    }
                    activities.push(activity);
                }
                Err(e) => {
                    errors.push(format!("Failed to parse perps order: {}", e));
                    tracing::warn!("Failed to parse perps order: {}", e);
                }
            }
        }

        // Process order updates if present
        if let Some(order_updates) = &request.activities.order_updates {
            for (index, order_update) in order_updates.iter().enumerate() {
                // Generate a unified oid-based hash to prevent duplicates with perps_order
                let synthetic_hash = format!("oid_{}", order_update.order.oid);

                match order_update.to_db_activity(&request.wallet_address, &synthetic_hash) {
                    Ok(mut activity) => {
                        // Mark as order update for universal update framework
                        if let Some(ref mut metadata) = activity.metadata {
                            if let Some(metadata_obj) = metadata.as_object_mut() {
                                metadata_obj.insert(
                                    "source".to_string(),
                                    serde_json::Value::String("order_update".to_string()),
                                );
                                metadata_obj.insert(
                                    "data_type".to_string(),
                                    serde_json::Value::String("order_status".to_string()),
                                );
                            }
                        }
                        activities.push(activity);
                    }
                    Err(e) => {
                        errors.push(format!("Failed to parse order update {}: {}", index, e));
                        tracing::warn!("Failed to parse order update {}: {}", index, e);
                    }
                }
            }
        }

        // Process TWAP fills history if present
        if let Some(twap_fills) = &request.activities.twap_fills_history {
            match twap_fills.to_db_activities(&request.wallet_address) {
                Ok(mut twap_activities) => {
                    // Mark as TWAP fills for universal update framework
                    for activity in &mut twap_activities {
                        if let Some(ref mut metadata) = activity.metadata {
                            if let Some(metadata_obj) = metadata.as_object_mut() {
                                metadata_obj.insert(
                                    "source".to_string(),
                                    serde_json::Value::String("twap_fills".to_string()),
                                );
                                metadata_obj.insert(
                                    "data_type".to_string(),
                                    serde_json::Value::String("twap_execution".to_string()),
                                );
                            }
                        }
                    }
                    activities.extend(twap_activities);
                }
                Err(e) => {
                    errors.push(format!("Failed to parse TWAP fills history: {}", e));
                    tracing::warn!("Failed to parse TWAP fills history: {}", e);
                }
            }
        }

        // Process deposits if present
        if let Some(deposits) = &request.activities.deposits {
            for deposit in deposits {
                match deposit.to_db_activity(&request.wallet_address) {
                    Ok(mut activity) => {
                        // Mark as deposit for universal update framework
                        if let Some(ref mut metadata) = activity.metadata {
                            if let Some(metadata_obj) = metadata.as_object_mut() {
                                metadata_obj.insert(
                                    "source".to_string(),
                                    serde_json::Value::String("deposit".to_string()),
                                );
                                metadata_obj.insert(
                                    "data_type".to_string(),
                                    serde_json::Value::String("fund_deposit".to_string()),
                                );
                            }
                        }
                        activities.push(activity);
                    }
                    Err(e) => {
                        errors.push(format!("Failed to parse deposit {}: {}", deposit.hash, e));
                        tracing::warn!("Failed to parse deposit {}: {}", deposit.hash, e);
                    }
                }
            }
        }

        // Process withdraws if present
        if let Some(withdraws) = &request.activities.withdraws {
            for withdraw in withdraws {
                match withdraw.to_db_activity(&request.wallet_address) {
                    Ok(mut activity) => {
                        // Mark as withdraw for universal update framework
                        if let Some(ref mut metadata) = activity.metadata {
                            if let Some(metadata_obj) = metadata.as_object_mut() {
                                metadata_obj.insert(
                                    "source".to_string(),
                                    serde_json::Value::String("withdraw".to_string()),
                                );
                                metadata_obj.insert(
                                    "data_type".to_string(),
                                    serde_json::Value::String("fund_withdraw".to_string()),
                                );
                            }
                        }
                        activities.push(activity);
                    }
                    Err(e) => {
                        errors.push(format!("Failed to parse withdraw {}: {}", withdraw.hash, e));
                        tracing::warn!("Failed to parse withdraw {}: {}", withdraw.hash, e);
                    }
                }
            }
        }

        // Log parsing results
        tracing::info!(
            "Parsed {} activities for wallet {}, {} errors (ledger_updates: {}, perp_orders: {}, order_updates: {}, twap_fills: {}, deposits: {}, withdraws: {}) [fills: {} - DISABLED]",
            activities.len(),
            request.wallet_address,
            errors.len(),
            request.activities.user_non_funding_ledger_updates.as_ref().map_or(0, |u| u.len()),
            if request.activities.perps_order.is_some() { 1 } else { 0 },
            request.activities.order_updates.as_ref().map_or(0, |s| s.len()),
            if request.activities.twap_fills_history.is_some() { 1 } else { 0 },
            request.activities.deposits.as_ref().map_or(0, |d| d.len()),
            request.activities.withdraws.as_ref().map_or(0, |w| w.len()),
            request.activities.user_fills.as_ref().map_or(0, |f| f.len())
        );

        if !errors.is_empty() {
            tracing::warn!("Parsing errors: {:?}", errors);
        }

        Ok(activities)
    }

    /// Validate the incoming request data
    pub fn validate_request(request: &RecordActivityRequest) -> Result<()> {
        // Validate wallet address format (basic check)
        if request.wallet_address.is_empty() {
            return Err(anyhow!("Wallet address cannot be empty"));
        }

        // Check if wallet address looks like a valid Ethereum address (0x + 40 hex chars)
        if !request.wallet_address.starts_with("0x") || request.wallet_address.len() != 42 {
            return Err(anyhow!("Invalid wallet address format"));
        }

        // Validate that at least one type of activity is provided
        // Allow empty arrays for batch uploads (frontend may send empty arrays)
        let has_fills = request.activities.user_fills.as_ref().map_or(false, |f| !f.is_empty());
        let has_updates = request
            .activities
            .user_non_funding_ledger_updates
            .as_ref()
            .map_or(false, |u| !u.is_empty());
        let has_perps_order = request.activities.perps_order.is_some();
        let has_order_updates =
            request.activities.order_updates.as_ref().map_or(false, |o| !o.is_empty());
        let has_twap_fills = request.activities.twap_fills_history.is_some();
        let has_deposits = request.activities.deposits.as_ref().map_or(false, |d| !d.is_empty());
        let has_withdraws = request.activities.withdraws.as_ref().map_or(false, |w| !w.is_empty());

        let has_data_fields = request.activities.user_fills.is_some() ||
            request.activities.user_non_funding_ledger_updates.is_some() ||
            request.activities.perps_order.is_some() ||
            request.activities.order_updates.is_some() ||
            request.activities.twap_fills_history.is_some() ||
            request.activities.deposits.is_some() ||
            request.activities.withdraws.is_some();

        if !has_fills &&
            !has_updates &&
            !has_perps_order &&
            !has_order_updates &&
            !has_twap_fills &&
            !has_deposits &&
            !has_withdraws &&
            !has_data_fields
        {
            return Err(anyhow!("No activities data provided"));
        }

        // Validate fills if present - ENABLED: UserFills processing enabled with aggregation
        if let Some(user_fills) = &request.activities.user_fills {
            for (index, fill) in user_fills.iter().enumerate() {
                Self::validate_fill(fill, index)?;
            }
        }

        // Validate ledger updates if present
        if let Some(user_updates) = &request.activities.user_non_funding_ledger_updates {
            for (index, update) in user_updates.iter().enumerate() {
                Self::validate_ledger_update(update, index)?;
            }
        }

        // Validate perps order if present
        if let Some(perps_order) = &request.activities.perps_order {
            Self::validate_perps_order(perps_order)?;
        }

        // Validate order updates if present
        if let Some(order_updates) = &request.activities.order_updates {
            for (index, order_update) in order_updates.iter().enumerate() {
                Self::validate_order_update(order_update, index)?;
            }
        }

        // Validate TWAP fills history if present
        if let Some(twap_fills) = &request.activities.twap_fills_history {
            Self::validate_twap_fills_history(twap_fills)?;
        }

        Ok(())
    }

    /// Validate a single fill entry
    fn validate_fill(fill: &HyperliquidFill, index: usize) -> Result<()> {
        if fill.coin.is_empty() {
            return Err(anyhow!("Fill[{}]: coin cannot be empty", index));
        }

        if fill.hash.is_empty() {
            return Err(anyhow!("Fill[{}]: hash cannot be empty", index));
        }

        if fill.side != "B" && fill.side != "A" {
            return Err(anyhow!(
                "Fill[{}]: invalid side '{}', must be 'B' or 'A'",
                index,
                fill.side
            ));
        }

        // Validate numeric string fields
        Self::validate_numeric_string(&fill.px, &format!("Fill[{}].px", index))?;
        Self::validate_numeric_string(&fill.sz, &format!("Fill[{}].sz", index))?;
        Self::validate_numeric_string(
            &fill.start_position,
            &format!("Fill[{}].start_position", index),
        )?;
        Self::validate_numeric_string(&fill.closed_pnl, &format!("Fill[{}].closed_pnl", index))?;
        Self::validate_numeric_string(&fill.fee, &format!("Fill[{}].fee", index))?;

        // Validate timestamp
        if fill.time <= 0 {
            return Err(anyhow!("Fill[{}]: invalid timestamp {}", index, fill.time));
        }

        Ok(())
    }

    /// Validate a single ledger update entry
    fn validate_ledger_update(
        update: &HyperliquidNonFundingLedgerUpdate,
        index: usize,
    ) -> Result<()> {
        if update.hash.is_empty() {
            return Err(anyhow!("LedgerUpdate[{}]: hash cannot be empty", index));
        }

        if update.time <= 0 {
            return Err(anyhow!("LedgerUpdate[{}]: invalid timestamp {}", index, update.time));
        }

        // Validate delta-specific fields based on type
        match &update.delta {
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::InternalTransfer {
                usdc,
                user,
                destination,
                fee,
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                Self::validate_numeric_string(fee, &format!("LedgerUpdate[{}].fee", index))?;
                if user.is_empty() || destination.is_empty() {
                    return Err(anyhow!(
                        "LedgerUpdate[{}]: user and destination cannot be empty",
                        index
                    ));
                }
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::AccountClassTransfer {
                usdc,
                ..
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::Deposit { usdc } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::Withdraw {
                usdc,
                fee,
                ..
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                Self::validate_numeric_string(fee, &format!("LedgerUpdate[{}].fee", index))?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::SpotTransfer {
                amount,
                usdc_value,
                user,
                destination,
                fee,
                native_token_fee,
                ..
            } => {
                Self::validate_numeric_string(amount, &format!("LedgerUpdate[{}].amount", index))?;
                Self::validate_numeric_string(
                    usdc_value,
                    &format!("LedgerUpdate[{}].usdc_value", index),
                )?;
                Self::validate_numeric_string(fee, &format!("LedgerUpdate[{}].fee", index))?;
                Self::validate_numeric_string(
                    native_token_fee,
                    &format!("LedgerUpdate[{}].native_token_fee", index),
                )?;
                if user.is_empty() || destination.is_empty() {
                    return Err(anyhow!(
                        "LedgerUpdate[{}]: user and destination cannot be empty",
                        index
                    ));
                }
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::Liquidation {
                liquidated_ntl_pos,
                account_value,
                ..
            } => {
                Self::validate_numeric_string(
                    liquidated_ntl_pos,
                    &format!("LedgerUpdate[{}].liquidated_ntl_pos", index),
                )?;
                Self::validate_numeric_string(
                    account_value,
                    &format!("LedgerUpdate[{}].account_value", index),
                )?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::RewardsClaim {
                amount,
            } => {
                Self::validate_numeric_string(amount, &format!("LedgerUpdate[{}].amount", index))?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::SubAccountTransfer {
                usdc,
                ..
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::VaultCreate {
                usdc,
                fee,
                vault,
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                Self::validate_numeric_string(fee, &format!("LedgerUpdate[{}].fee", index))?;
                if vault.is_empty() {
                    return Err(anyhow!("LedgerUpdate[{}]: vault address cannot be empty", index));
                }
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::VaultDeposit {
                usdc,
                vault,
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                if vault.is_empty() {
                    return Err(anyhow!("LedgerUpdate[{}]: vault address cannot be empty", index));
                }
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::VaultDistribution {
                usdc,
                vault,
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                if vault.is_empty() {
                    return Err(anyhow!("LedgerUpdate[{}]: vault address cannot be empty", index));
                }
            }
            crate::models::hyperliquid_activity::HyperliquidLedgerDelta::VaultWithdraw {
                usdc,
                vault,
            } => {
                Self::validate_numeric_string(usdc, &format!("LedgerUpdate[{}].usdc", index))?;
                if vault.is_empty() {
                    return Err(anyhow!("LedgerUpdate[{}]: vault address cannot be empty", index));
                }
            }
        }

        Ok(())
    }

    /// Validate that a string can be parsed as a number
    fn validate_numeric_string(value: &str, field_name: &str) -> Result<()> {
        if value.is_empty() {
            return Err(anyhow!("{}: cannot be empty", field_name));
        }

        value
            .parse::<f64>()
            .map_err(|_| anyhow!("{}: '{}' is not a valid number", field_name, value))?;

        Ok(())
    }

    /// Remove duplicate activities based on transaction hash and wallet address
    pub fn deduplicate_activities(activities: Vec<DbWalletActivity>) -> Vec<DbWalletActivity> {
        let mut seen = HashMap::new();
        let mut deduplicated = Vec::new();
        let original_count = activities.len();

        for activity in activities {
            let key = format!("{}:{}", activity.wallet_address, activity.tx_signature);

            if !seen.contains_key(&key) {
                seen.insert(key, true);
                deduplicated.push(activity);
            } else {
                tracing::debug!("Skipping duplicate activity: {}", activity.tx_signature);
            }
        }

        if deduplicated.len() < original_count {
            tracing::info!(
                "Removed {} duplicate activities, {} remaining",
                original_count - deduplicated.len(),
                deduplicated.len()
            );
        }

        deduplicated
    }

    /// Get statistics about the parsed activities
    pub fn get_activity_stats(activities: &[DbWalletActivity]) -> HashMap<String, usize> {
        let mut stats = HashMap::new();

        for activity in activities {
            let activity_type = activity.activity_type.to_string();
            *stats.entry(activity_type).or_insert(0) += 1;
        }

        stats
    }





    /// Generate unique transaction signature for system operations with zero hash
    /// Returns a shorter hash (0x + 32 hex chars = 34 chars) to distinguish from normal tx hashes
    /// (66 chars)
    pub fn generate_unique_tx_signature(
        wallet_address: &str,
        original_hash: &str,
        timestamp: i64,
        operation_type: &str,
        sequence_id: Option<u64>,
    ) -> String {
        // Create a unique identifier based on multiple factors
        let mut hasher = Sha256::new();
        hasher.update(wallet_address.as_bytes());
        hasher.update(original_hash.as_bytes());
        hasher.update(timestamp.to_le_bytes());
        hasher.update(operation_type.as_bytes());

        // Add sequence ID if provided for additional uniqueness
        if let Some(seq_id) = sequence_id {
            hasher.update(seq_id.to_le_bytes());
        }

        let hash_result = hasher.finalize();
        // Use only first 16 bytes (32 hex chars) to make it shorter than normal tx hash (64 hex
        // chars) Format: 0x + 32 hex chars = 34 total chars (vs normal 66 chars)
        format!("0x{}", hex::encode(&hash_result[..16]))
    }

    /// Check if a hash is the zero hash used by system operations
    pub fn is_system_operation_hash(hash: &str) -> bool {
        hash == "0x0000000000000000000000000000000000000000000000000000000000000000"
    }

    /// Check if a hash is a generated system operation hash (34 chars vs normal 66 chars)
    pub fn is_generated_system_hash(hash: &str) -> bool {
        hash.starts_with("0x") && hash.len() == 34
    }

    /// Determine operation type from metadata
    pub fn determine_operation_type(metadata: &serde_json::Value) -> String {
        if let Some(direction) = metadata.get("direction").and_then(|d| d.as_str()) {
            if direction.contains("Dust Conversion") {
                return "dust_conversion".to_string();
            }
        }

        if let Some(order_type) = metadata.get("order_type").and_then(|o| o.as_str()) {
            if order_type == "TWAP" {
                return "twap_slice".to_string();
            }
        }

        // Default for other system operations
        "system_operation".to_string()
    }

    /// Aggregate fills by order ID to create order-level wallet activities
    pub fn aggregate_fills_by_order(
        user_fills: &[HyperliquidFill],
        wallet_address: &str,
    ) -> Result<Vec<DbWalletActivity>> {
        use crate::models::hyperliquid_activity::AggregatedOrderData;
        use std::collections::HashMap;

        // Group fills by oid
        let mut fills_by_oid: HashMap<i64, Vec<HyperliquidFill>> = HashMap::new();

        for fill in user_fills {
            fills_by_oid.entry(fill.oid).or_insert_with(Vec::new).push(fill.clone());
        }

        let mut activities = Vec::new();
        let mut errors = Vec::new();

        // Process each order (group of fills with same oid)
        for (oid, fills) in fills_by_oid {
            tracing::info!("Processing order {} with {} fills", oid, fills.len());

            match AggregatedOrderData::from_fills(fills) {
                Ok(aggregated_order) => {
                    tracing::info!(
                        "Successfully created aggregated order {} for {} fills",
                        oid,
                        aggregated_order.fill_count
                    );

                    match aggregated_order.to_db_activity(wallet_address) {
                        Ok(activity) => {
                            activities.push(activity);
                            tracing::info!(
                                "Successfully converted order {} to activity: {} {} at avg price {}",
                                oid,
                                aggregated_order.total_size,
                                aggregated_order.coin,
                                aggregated_order.avg_price
                            );
                        }
                        Err(e) => {
                            errors.push(format!(
                                "Failed to convert aggregated order {} to activity: {}",
                                oid, e
                            ));
                            tracing::error!(
                                "Failed to convert aggregated order {} to activity: {}",
                                oid,
                                e
                            );
                        }
                    }
                }
                Err(e) => {
                    errors.push(format!("Failed to aggregate fills for order {}: {}", oid, e));
                    tracing::error!("Failed to aggregate fills for order {}: {}", oid, e);
                }
            }
        }

        if !errors.is_empty() {
            tracing::warn!("Errors during fill aggregation: {:?}", errors);
        }

        Ok(activities)
    }

    /// Validate activity data integrity
    pub fn validate_activity_integrity(activities: &[DbWalletActivity]) -> Result<()> {
        for (index, activity) in activities.iter().enumerate() {
            // Check for required fields
            if activity.wallet_address.is_empty() {
                return Err(anyhow!("Activity[{}]: wallet_address is empty", index));
            }

            if activity.tx_signature.is_empty() {
                return Err(anyhow!("Activity[{}]: tx_signature is empty", index));
            }

            // Validate timestamp is reasonable
            if activity.timestamp <= 0 {
                return Err(anyhow!(
                    "Activity[{}]: invalid timestamp {}",
                    index,
                    activity.timestamp
                ));
            }

            // Check chain value
            if activity.chain < 0 || activity.chain > 2 {
                return Err(anyhow!("Activity[{}]: invalid chain value {}", index, activity.chain));
            }

            // Validate amounts if present
            if let Some(amount) = activity.token_amount {
                if amount < 0 {
                    return Err(anyhow!("Activity[{}]: negative token_amount {}", index, amount));
                }
            }

            if let Some(amount) = activity.base_amount {
                if amount < 0 {
                    return Err(anyhow!("Activity[{}]: negative base_amount {}", index, amount));
                }
            }
        }

        Ok(())
    }

    /// Validate a perps order entry
    fn validate_perps_order(perps_order: &PerpsOrder) -> Result<()> {
        // Validate required fields
        if perps_order.symbol.is_empty() {
            return Err(anyhow!("PerpsOrder: symbol cannot be empty"));
        }
        if perps_order.token_image.is_empty() {
            return Err(anyhow!("PerpsOrder: token_image cannot be empty"));
        }

        // Validate leverage if provided
        if let Some(leverage) = perps_order.leverage {
            if leverage <= 0.0 {
                return Err(anyhow!("PerpsOrder: leverage must be positive"));
            }
        }

        // Validate leverage type
        if !matches!(perps_order.leverage_type.as_str(), "Cross" | "Isolated") {
            return Err(anyhow!(
                "PerpsOrder: invalid leverage_type '{}'",
                perps_order.leverage_type
            ));
        }

        // Validate that at least one order type is present
        if perps_order.order.is_none() && perps_order.twap_order.is_none() {
            return Err(anyhow!("PerpsOrder: either order or twapOrder must be provided"));
        }

        // Validate order parameters if present
        if let Some(order) = &perps_order.order {
            Self::validate_order_parameters(order)?;
        }

        // Validate TWAP order parameters if present
        if let Some(twap_order) = &perps_order.twap_order {
            Self::validate_twap_order_parameters(twap_order)?;
        }

        Ok(())
    }

    /// Validate order parameters
    fn validate_order_parameters(order: &OrderParameters) -> Result<()> {
        if order.orders.is_empty() {
            return Err(anyhow!("OrderParameters: orders array cannot be empty"));
        }

        for (index, order_detail) in order.orders.iter().enumerate() {
            Self::validate_numeric_string(
                &order_detail.p,
                &format!("OrderParameters.orders[{}].p", index),
            )?;
            Self::validate_numeric_string(
                &order_detail.s,
                &format!("OrderParameters.orders[{}].s", index),
            )?;
        }

        Ok(())
    }

    /// Validate TWAP order parameters
    fn validate_twap_order_parameters(twap_order: &TwapOrderParameters) -> Result<()> {
        Self::validate_numeric_string(&twap_order.s, "TwapOrderParameters.s")?;

        if twap_order.m <= 0 {
            return Err(anyhow!("TwapOrderParameters: m (duration) must be positive"));
        }

        Ok(())
    }

    /// Validate an order update entry
    fn validate_order_update(order_update: &OrderUpdate, index: usize) -> Result<()> {
        // Validate required fields
        if order_update.order.coin.is_empty() {
            return Err(anyhow!("OrderUpdate[{}]: order.coin cannot be empty", index));
        }
        if order_update.status.is_empty() {
            return Err(anyhow!("OrderUpdate[{}]: status cannot be empty", index));
        }

        // Validate numeric fields
        Self::validate_numeric_string(
            &order_update.order.limit_px,
            &format!("OrderUpdate[{}].order.limit_px", index),
        )?;
        Self::validate_numeric_string(
            &order_update.order.sz,
            &format!("OrderUpdate[{}].order.sz", index),
        )?;
        Self::validate_numeric_string(
            &order_update.order.orig_sz,
            &format!("OrderUpdate[{}].order.orig_sz", index),
        )?;

        // Validate side
        if !matches!(order_update.order.side.as_str(), "B" | "A") {
            return Err(anyhow!(
                "OrderUpdate[{}]: invalid side '{}'",
                index,
                order_update.order.side
            ));
        }

        // Validate timestamps
        if order_update.status_timestamp <= 0 {
            return Err(anyhow!("OrderUpdate[{}]: status_timestamp must be positive", index));
        }
        if order_update.order.timestamp <= 0 {
            return Err(anyhow!("OrderUpdate[{}]: order.timestamp must be positive", index));
        }

        Ok(())
    }

    /// Validate TWAP fills history
    fn validate_twap_fills_history(twap_fills: &WsUserTwapSliceFills) -> Result<()> {
        // Validate required fields
        if twap_fills.user.is_empty() {
            return Err(anyhow!("WsUserTwapSliceFills: user cannot be empty"));
        }

        // Validate each TWAP slice fill
        for (index, twap_fill) in twap_fills.twap_slice_fills.iter().enumerate() {
            Self::validate_twap_slice_fill(twap_fill, index)?;
        }

        Ok(())
    }

    /// Validate a single TWAP slice fill
    fn validate_twap_slice_fill(twap_fill: &TwapSliceFill, index: usize) -> Result<()> {
        // Validate TWAP ID
        if twap_fill.twap_id <= 0 {
            return Err(anyhow!("TwapSliceFill[{}]: twap_id must be positive", index));
        }

        // The fill validation is handled by the existing HyperEvmFill validation
        // when it gets converted to DbWalletActivity

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::{
        hyperliquid_activity::{
            HyperliquidFill, HyperliquidLedgerDelta, HyperliquidNonFundingLedgerUpdate,
            LimitOrderParams, NestedActivities, Order, OrderDetail, OrderParameters, OrderType,
            OrderUpdate, PerpsOrder, TwapOrderParameters, TwapSliceFill, WsUserTwapSliceFills,
        },
        Deposit, Withdraw,
    };

    #[test]
    fn test_validate_request_empty_wallet() {
        use crate::models::hyperliquid_activity::NestedActivities;

        let request = RecordActivityRequest {
            wallet_address: "".to_string(),
            is_hyperliquid_mainnet: true,
            activities: NestedActivities {
                user_fills: None,
                user_non_funding_ledger_updates: None,
                perps_order: None,
                order_updates: None,
                twap_fills_history: None,
                deposits: None,
                withdraws: None,
            },
        };

        assert!(HyperliquidParser::validate_request(&request).is_err());
    }

    #[test]
    fn test_validate_perps_order_success() {
        let perps_order = PerpsOrder {
            order: Some(OrderParameters {
                orders: vec![OrderDetail {
                    a: 0,    // BTC asset ID
                    b: true, // long position
                    p: "50000.0".to_string(),
                    s: "0.1".to_string(),
                    r: false, // not reduce-only
                    t: OrderType::Limit { limit: LimitOrderParams { tif: "Gtc".to_string() } },
                    c: Some("test_order_123".to_string()),
                }],
                grouping: "na".to_string(),
            }),
            twap_order: None,
            order_response: None,
            twap_order_response: None,
            symbol: "BTC/USDC".to_string(),
            token_image: "https://example.com/btc.png".to_string(),
            leverage: Some(10.0),
            leverage_type: "Cross".to_string(),
        };

        assert!(HyperliquidParser::validate_perps_order(&perps_order).is_ok());
    }

    #[test]
    fn test_validate_perps_order_invalid_leverage_type() {
        let perps_order = PerpsOrder {
            order: Some(OrderParameters { orders: vec![], grouping: "na".to_string() }),
            twap_order: None,
            order_response: None,
            twap_order_response: None,
            symbol: "BTC/USDC".to_string(),
            token_image: "https://example.com/btc.png".to_string(),
            leverage: Some(10.0),
            leverage_type: "Invalid".to_string(), // Invalid leverage type
        };

        assert!(HyperliquidParser::validate_perps_order(&perps_order).is_err());
    }

    #[test]
    fn test_validate_deposit_success() {
        // Test deposit validation would be implemented here
        assert!(true);
    }

    #[test]
    fn test_validate_withdraw_success() {
        // Test withdraw validation would be implemented here
        assert!(true);
    }

    #[test]
    fn test_validate_withdraw_missing_required_fields() {
        // Test invalid withdraw validation would be implemented here
        assert!(true);
    }

    #[test]
    fn test_perp_order_to_db_activity() {
        // Test would need to be implemented with correct PerpsOrder structure
        // For now, just test that the parser validates requests correctly
        assert!(true);
    }

    #[test]
    fn test_deposit_to_db_activity() {
        // Test would need to be implemented with correct Deposit structure
        // For now, just test basic functionality
        assert!(true);
    }

    // #[test]
    // fn test_validate_request_invalid_wallet_format() {
    //     let request = RecordActivityRequest {
    //         wallet_address: "invalid_address".to_string(),
    //         fills: None,
    //         non_funding_ledger_updates: None,
    //     };

    //     assert!(HyperEvmParser::validate_request(&request).is_err());
    // }

    // #[test]
    // fn test_validate_request_no_activities() {
    //     let request = RecordActivityRequest {
    //         wallet_address: "******************************************".to_string(),
    //         fills: None,
    //         non_funding_ledger_updates: None,
    //     };

    //     assert!(HyperEvmParser::validate_request(&request).is_err());
    // }

    #[test]
    fn test_validate_numeric_string() {
        assert!(HyperliquidParser::validate_numeric_string("123.45", "test").is_ok());
        assert!(HyperliquidParser::validate_numeric_string("-123.45", "test").is_ok());
        assert!(HyperliquidParser::validate_numeric_string("0", "test").is_ok());
        assert!(HyperliquidParser::validate_numeric_string("", "test").is_err());
        assert!(HyperliquidParser::validate_numeric_string("abc", "test").is_err());
    }

    #[test]
    fn test_deduplicate_activities() {
        // This test would need actual DbWalletActivity instances
        // For now, just test that the function exists and can be called
        let activities = vec![];
        let result = HyperliquidParser::deduplicate_activities(activities);
        assert_eq!(result.len(), 0);
    }
}
